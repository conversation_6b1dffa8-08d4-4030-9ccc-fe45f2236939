===============================================
    NarratoAI 启动问题解决方案
===============================================

如果您的 start.bat 脚本出现闪退问题，请使用以下解决方案：

===============================================
推荐使用方法（简单版本）
===============================================

【最简单】双击运行：simple_menu.bat
- 提供友好的菜单界面
- 包含所有功能选项
- 适合所有用户

【手动运行】按顺序执行：
1. simple_check.bat   (检查系统)
2. simple_fix.bat     (修复问题)  
3. simple_start.bat   (启动应用)

===============================================
文件说明
===============================================

simple_menu.bat
- 主菜单脚本，推荐使用
- 集成所有功能
- 英文界面，避免编码问题

simple_check.bat  
- 系统检查脚本
- 检查6个关键项目
- 生成详细检查报告

simple_fix.bat
- 问题修复脚本  
- 修复5类常见问题
- 自动清理和配置

simple_start.bat
- 安全启动脚本
- 增强错误处理
- 详细启动日志

===============================================
与原版脚本的改进
===============================================

1. 编码兼容性
   - 使用英文界面避免乱码
   - 兼容不同Windows版本
   - 稳定的字符处理

2. 错误处理
   - 详细的错误检查
   - 优雅的错误恢复
   - 清晰的错误提示

3. 日志记录
   - 完整的操作日志
   - 便于问题诊断
   - 详细的状态报告

4. 自动修复
   - 自动检测常见问题
   - 智能修复机制
   - 减少手动操作

===============================================
使用步骤
===============================================

步骤1：运行检查
双击 simple_check.bat 或通过菜单选择"Check System"
- 检查所有依赖项
- 生成检查报告
- 识别潜在问题

步骤2：修复问题（如果有错误）
双击 simple_fix.bat 或通过菜单选择"Fix Issues"
- 自动修复常见问题
- 安装缺失模块
- 清理缓存文件

步骤3：启动应用
双击 simple_start.bat 或通过菜单选择"Start NarratoAI"
- 安全启动应用
- 详细错误日志
- 自动环境配置

===============================================
常见问题解决
===============================================

问题：脚本一闪就关闭
解决：
1. 运行 simple_check.bat 检查问题
2. 运行 simple_fix.bat 修复问题
3. 使用 simple_start.bat 启动

问题：提示权限不足
解决：
1. 右键点击脚本
2. 选择"以管理员身份运行"

问题：提示缺少文件
解决：
1. 检查 lib 目录是否完整
2. 重新解压安装包
3. 确保所有文件都存在

问题：Python 相关错误
解决：
1. 运行 simple_fix.bat 自动修复
2. 手动安装 Visual C++ Redistributable
3. 检查防病毒软件设置

问题：端口被占用
解决：
1. 运行 simple_fix.bat 自动处理
2. 手动结束占用 8501 端口的进程
3. 重启计算机

问题：防病毒软件报警
解决：
1. 将 NarratoAI 目录添加到白名单
2. 临时关闭实时保护
3. 信任相关可执行文件

===============================================
日志文件说明
===============================================

check_report.txt - 系统检查详细报告
fix_report.txt - 问题修复操作记录  
startup.log - 应用启动详细日志

这些日志文件包含详细的操作信息，
如果问题仍然存在，请查看相应日志。

===============================================
系统要求
===============================================

操作系统：Windows 7/8/10/11
架构：支持 32位 和 64位
运行时：Microsoft Visual C++ Redistributable
网络：需要网络连接（首次安装模块时）
权限：建议以管理员身份运行

===============================================
技术支持
===============================================

如果按照上述方法仍无法解决问题：

1. 保存所有日志文件
2. 记录具体错误信息  
3. 提供系统环境信息
4. 联系技术支持

常用下载链接：
Visual C++ Redistributable:
https://aka.ms/vs/17/release/vc_redist.x64.exe

===============================================
文件清单
===============================================

推荐使用（简单版本）：
- simple_menu.bat     (主菜单)
- simple_check.bat    (系统检查)
- simple_fix.bat      (问题修复)
- simple_start.bat    (安全启动)

备用版本（功能完整但可能有编码问题）：
- menu.bat            (中文菜单)
- check_system.bat    (中文检查)
- fix_issues.bat      (中文修复)
- start_safe.bat      (中文启动)

说明文档：
- 解决方案说明.txt   (本文件)
- 使用说明.txt       (详细说明)

===============================================

建议优先使用 simple_menu.bat 开始！
