@echo off
:: =============================================================================
:: NarratoAI 常见问题修复脚本
:: 自动修复可能导致启动失败的常见问题
:: =============================================================================

title NarratoAI 问题修复工具
setlocal enabledelayedexpansion

echo ===============================================
echo    NarratoAI 问题修复工具
echo ===============================================
echo.

set "SCRIPT_DIR=%~dp0"
set "LOG_FILE=%SCRIPT_DIR%fix_log.txt"

echo 开始修复常见问题... > "%LOG_FILE%"
echo 修复时间: %date% %time% >> "%LOG_FILE%"
echo ============================================== >> "%LOG_FILE%"

:: =============================================================================
:: 1. 修复配置文件
:: =============================================================================
echo [1/6] 检查并修复配置文件...
echo [1/6] 检查并修复配置文件... >> "%LOG_FILE%"

if not exist "%SCRIPT_DIR%NarratoAI\config.toml" (
    if exist "%SCRIPT_DIR%NarratoAI\config.example.toml" (
        echo 复制示例配置文件...
        copy "%SCRIPT_DIR%NarratoAI\config.example.toml" "%SCRIPT_DIR%NarratoAI\config.toml" >nul 2>&1
        if !errorlevel! equ 0 (
            echo ✓ 已创建配置文件
            echo ✓ 已创建配置文件 >> "%LOG_FILE%"
        ) else (
            echo ✗ 无法创建配置文件
            echo ✗ 无法创建配置文件 >> "%LOG_FILE%"
        )
    ) else (
        echo ⚠ 警告: 示例配置文件不存在
        echo ⚠ 警告: 示例配置文件不存在 >> "%LOG_FILE%"
    )
) else (
    echo ✓ 配置文件已存在
    echo ✓ 配置文件已存在 >> "%LOG_FILE%"
)

:: =============================================================================
:: 2. 修复Streamlit配置
:: =============================================================================
echo.
echo [2/6] 修复Streamlit配置...
echo [2/6] 修复Streamlit配置... >> "%LOG_FILE%"

set "STREAMLIT_DIR=%USERPROFILE%\.streamlit"
set "CONFIG_FILE=%STREAMLIT_DIR%\config.toml"
set "CREDENTIAL_FILE=%STREAMLIT_DIR%\credentials.toml"

:: 创建Streamlit目录
if not exist "%STREAMLIT_DIR%" (
    mkdir "%STREAMLIT_DIR%" 2>nul
    if !errorlevel! equ 0 (
        echo ✓ 已创建Streamlit配置目录
        echo ✓ 已创建Streamlit配置目录 >> "%LOG_FILE%"
    ) else (
        echo ✗ 无法创建Streamlit配置目录
        echo ✗ 无法创建Streamlit配置目录 >> "%LOG_FILE%"
    )
)

:: 创建配置文件
if not exist "%CONFIG_FILE%" (
    (
        echo [global]
        echo developmentMode = false
        echo 
        echo [server]
        echo headless = true
        echo enableCORS = true
        echo enableXsrfProtection = false
        echo maxUploadSize = 2048
        echo 
        echo [browser]
        echo serverAddress = "127.0.0.1"
        echo gatherUsageStats = false
    ) > "%CONFIG_FILE%" 2>nul
    if !errorlevel! equ 0 (
        echo ✓ 已创建Streamlit配置文件
        echo ✓ 已创建Streamlit配置文件 >> "%LOG_FILE%"
    )
)

:: 创建凭证文件
if not exist "%CREDENTIAL_FILE%" (
    (
        echo [general]
        echo email = ""
    ) > "%CREDENTIAL_FILE%" 2>nul
    if !errorlevel! equ 0 (
        echo ✓ 已创建Streamlit凭证文件
        echo ✓ 已创建Streamlit凭证文件 >> "%LOG_FILE%"
    )
)

:: =============================================================================
:: 3. 检查并修复Python环境
:: =============================================================================
echo.
echo [3/6] 检查Python环境...
echo [3/6] 检查Python环境... >> "%LOG_FILE%"

set "PYTHON_EXE=%SCRIPT_DIR%lib\python\python.exe"
if exist "%PYTHON_EXE%" (
    echo ✓ Python解释器存在
    echo ✓ Python解释器存在 >> "%LOG_FILE%"
    
    :: 检查Streamlit模块
    "%PYTHON_EXE%" -c "import streamlit" >nul 2>&1
    if !errorlevel! neq 0 (
        echo 尝试安装Streamlit...
        echo 尝试安装Streamlit... >> "%LOG_FILE%"
        "%PYTHON_EXE%" -m pip install streamlit --quiet --no-warn-script-location 2>>"%LOG_FILE%"
        if !errorlevel! equ 0 (
            echo ✓ Streamlit安装成功
            echo ✓ Streamlit安装成功 >> "%LOG_FILE%"
        ) else (
            echo ✗ Streamlit安装失败
            echo ✗ Streamlit安装失败 >> "%LOG_FILE%"
        )
    ) else (
        echo ✓ Streamlit模块可用
        echo ✓ Streamlit模块可用 >> "%LOG_FILE%"
    )
) else (
    echo ✗ Python解释器不存在
    echo ✗ Python解释器不存在 >> "%LOG_FILE%"
)

:: =============================================================================
:: 4. 清理临时文件和缓存
:: =============================================================================
echo.
echo [4/6] 清理临时文件...
echo [4/6] 清理临时文件... >> "%LOG_FILE%"

:: 清理Python缓存
if exist "%SCRIPT_DIR%NarratoAI\__pycache__" (
    rmdir /s /q "%SCRIPT_DIR%NarratoAI\__pycache__" 2>nul
    echo ✓ 已清理Python缓存
    echo ✓ 已清理Python缓存 >> "%LOG_FILE%"
)

:: 清理Streamlit缓存
if exist "%USERPROFILE%\.streamlit\cache" (
    rmdir /s /q "%USERPROFILE%\.streamlit\cache" 2>nul
    echo ✓ 已清理Streamlit缓存
    echo ✓ 已清理Streamlit缓存 >> "%LOG_FILE%"
)

:: 清理临时文件
for %%f in ("%SCRIPT_DIR%*.tmp" "%SCRIPT_DIR%*.temp" "%SCRIPT_DIR%temp_*") do (
    if exist "%%f" (
        del "%%f" 2>nul
        echo ✓ 已删除临时文件: %%~nxf
        echo ✓ 已删除临时文件: %%~nxf >> "%LOG_FILE%"
    )
)

:: =============================================================================
:: 5. 修复文件权限
:: =============================================================================
echo.
echo [5/6] 检查文件权限...
echo [5/6] 检查文件权限... >> "%LOG_FILE%"

:: 测试写入权限
echo test > "%SCRIPT_DIR%permission_test.tmp" 2>nul
if exist "%SCRIPT_DIR%permission_test.tmp" (
    del "%SCRIPT_DIR%permission_test.tmp" 2>nul
    echo ✓ 具有写入权限
    echo ✓ 具有写入权限 >> "%LOG_FILE%"
) else (
    echo ✗ 缺少写入权限，建议以管理员身份运行
    echo ✗ 缺少写入权限，建议以管理员身份运行 >> "%LOG_FILE%"
)

:: =============================================================================
:: 6. 检查端口占用
:: =============================================================================
echo.
echo [6/6] 检查端口占用...
echo [6/6] 检查端口占用... >> "%LOG_FILE%"

netstat -an 2>nul | findstr ":8501" >nul
if !errorlevel! equ 0 (
    echo ⚠ 警告: 端口8501被占用，尝试结束相关进程...
    echo ⚠ 警告: 端口8501被占用 >> "%LOG_FILE%"
    
    :: 尝试结束占用端口的进程
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":8501"') do (
        taskkill /pid %%a /f >nul 2>&1
        if !errorlevel! equ 0 (
            echo ✓ 已结束进程 PID: %%a
            echo ✓ 已结束进程 PID: %%a >> "%LOG_FILE%"
        )
    )
) else (
    echo ✓ 端口8501可用
    echo ✓ 端口8501可用 >> "%LOG_FILE%"
)

:: =============================================================================
:: 修复完成
:: =============================================================================
echo.
echo ==============================================
echo 修复完成！
echo ==============================================
echo.
echo 修复日志已保存到: %LOG_FILE%
echo.
echo 建议操作:
echo 1. 重新运行 start_improved.bat 启动应用
echo 2. 如果仍有问题，运行 diagnose_simple.bat 进行诊断
echo 3. 确保以管理员身份运行脚本（如果需要）
echo.

echo 修复完成！ >> "%LOG_FILE%"
echo 修复时间: %date% %time% >> "%LOG_FILE%"

echo 按任意键退出...
pause >nul
