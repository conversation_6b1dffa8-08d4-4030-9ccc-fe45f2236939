@echo off
:: =============================================================================
:: NarratoAI 改进版启动脚本
:: 具有更好的错误处理、兼容性和诊断功能
:: =============================================================================

:: 设置控制台标题和编码
title NarratoAI 启动器
setlocal enabledelayedexpansion

:: 尝试设置UTF-8编码，如果失败则继续使用默认编码
chcp 65001 >nul 2>&1
if !errorlevel! neq 0 (
    echo 警告: 无法设置UTF-8编码，使用默认编码
)

:: 创建启动日志
set "LOG_FILE=%~dp0startup.log"
echo NarratoAI 启动日志 > "%LOG_FILE%"
echo 启动时间: %date% %time% >> "%LOG_FILE%"
echo ============================================== >> "%LOG_FILE%"

echo ===============================================
echo    NarratoAI 智能视频生成工具
echo ===============================================
echo.

:: 获取脚本目录并验证
set "CURRENT_DIR=%~dp0"
call :log "当前工作目录: %CURRENT_DIR%"

:: 检查是否在正确的目录中运行
if not exist "%CURRENT_DIR%NarratoAI" (
    call :log "错误: 未找到NarratoAI目录，请确保在正确的位置运行此脚本"
    call :error_exit "目录结构错误"
)

:: =============================================================================
:: 环境变量配置
:: =============================================================================
call :log "配置环境变量..."

:: FFmpeg配置
set "FFMPEG_BINARY=%CURRENT_DIR%lib\ffmpeg\ffmpeg-7.0-essentials_build\ffmpeg.exe"
set "FFMPEG_PATH=%CURRENT_DIR%lib\ffmpeg\ffmpeg-7.0-essentials_build"

:: ImageMagick配置
set "IMAGEMAGICK_BINARY=%CURRENT_DIR%lib\imagemagic\ImageMagick-7.1.1-29-portable-Q16-x64\magick.exe"
set "IMAGEMAGICK_PATH=%CURRENT_DIR%lib\imagemagic\ImageMagick-7.1.1-29-portable-Q16-x64"

:: Python配置
set "PYTHON_BINARY=%CURRENT_DIR%lib\python\python.exe"
set "PYTHONPATH=%CURRENT_DIR%NarratoAI;%PYTHONPATH%"

:: 项目环境变量
set "NARRATO_ROOT=%CURRENT_DIR%NarratoAI"
set "NARRATO_FFMPEG=%FFMPEG_BINARY%"
set "NARRATO_IMAGEMAGICK=%IMAGEMAGICK_BINARY%"

:: 安全地添加到PATH（避免PATH过长问题）
call :add_to_path "%FFMPEG_PATH%"
call :add_to_path "%IMAGEMAGICK_PATH%"

:: =============================================================================
:: 依赖检查
:: =============================================================================
call :log "检查依赖项..."

:: 检查Python
if not exist "%PYTHON_BINARY%" (
    call :log "错误: Python解释器不存在: %PYTHON_BINARY%"
    call :error_exit "Python环境缺失"
)

:: 测试Python是否可以运行
"%PYTHON_BINARY%" --version >nul 2>&1
if !errorlevel! neq 0 (
    call :log "错误: Python无法正常运行，可能缺少运行时库"
    call :suggest_vcredist
    call :error_exit "Python运行时错误"
)
call :log "✓ Python环境正常"

:: 检查FFmpeg
if not exist "%FFMPEG_BINARY%" (
    call :log "错误: FFmpeg不存在: %FFMPEG_BINARY%"
    call :error_exit "FFmpeg缺失"
)
call :log "✓ FFmpeg存在"

:: 检查ImageMagick
if not exist "%IMAGEMAGICK_BINARY%" (
    call :log "错误: ImageMagick不存在: %IMAGEMAGICK_BINARY%"
    call :error_exit "ImageMagick缺失"
)
call :log "✓ ImageMagick存在"

:: 检查主程序文件
if not exist "%CURRENT_DIR%NarratoAI\webui.py" (
    call :log "错误: 主程序文件webui.py不存在"
    call :error_exit "主程序文件缺失"
)
call :log "✓ 主程序文件存在"

:: =============================================================================
:: Streamlit配置
:: =============================================================================
call :log "配置Streamlit..."

set "USER_HOME=%USERPROFILE%"
set "STREAMLIT_DIR=%USER_HOME%\.streamlit"
set "CREDENTIAL_FILE=%STREAMLIT_DIR%\credentials.toml"

:: 创建Streamlit配置目录
if not exist "%STREAMLIT_DIR%" (
    call :log "创建Streamlit配置目录..."
    mkdir "%STREAMLIT_DIR%" 2>nul
    if !errorlevel! neq 0 (
        call :log "警告: 无法创建Streamlit配置目录，可能影响功能"
    )
)

:: 创建凭证文件
if not exist "%CREDENTIAL_FILE%" (
    if exist "%STREAMLIT_DIR%" (
        (
            echo [general]
            echo email=""
        ) > "%CREDENTIAL_FILE%" 2>nul
        if !errorlevel! equ 0 (
            call :log "✓ Streamlit配置文件已创建"
        )
    )
)

:: =============================================================================
:: 检查Streamlit模块
:: =============================================================================
call :log "检查Streamlit模块..."
"%PYTHON_BINARY%" -c "import streamlit" >nul 2>&1
if !errorlevel! neq 0 (
    call :log "错误: Streamlit模块不可用"
    call :log "尝试安装Streamlit..."
    "%PYTHON_BINARY%" -m pip install streamlit >nul 2>&1
    if !errorlevel! neq 0 (
        call :log "错误: 无法安装Streamlit，请检查网络连接"
        call :error_exit "Streamlit模块缺失"
    )
    call :log "✓ Streamlit安装成功"
) else (
    call :log "✓ Streamlit模块可用"
)

:: =============================================================================
:: 启动应用
:: =============================================================================
call :log "准备启动应用..."

:: 切换到项目目录
cd /d "%CURRENT_DIR%NarratoAI" 2>nul
if !errorlevel! neq 0 (
    call :log "错误: 无法切换到项目目录"
    call :error_exit "目录访问错误"
)

call :log "切换工作目录到: %CURRENT_DIR%NarratoAI"
call :log "正在启动NarratoAI应用..."

echo.
echo 正在启动应用，请稍候...
echo 启动后将自动打开浏览器窗口
echo 如果浏览器未自动打开，请手动访问: http://127.0.0.1:8501
echo.

:: 启动Streamlit应用
"%PYTHON_BINARY%" -m streamlit run webui.py --browser.serverAddress="127.0.0.1" --server.enableCORS=True --server.maxUploadSize=2048 --browser.gatherUsageStats=False 2>&1
set "EXIT_CODE=!errorlevel!"

:: 记录退出状态
call :log "应用退出，退出代码: %EXIT_CODE%"

if %EXIT_CODE% neq 0 (
    echo.
    echo 应用异常退出，退出代码: %EXIT_CODE%
    echo 请查看启动日志: %LOG_FILE%
    echo.
    echo 常见解决方案:
    echo 1. 检查端口8501是否被其他程序占用
    echo 2. 以管理员身份运行此脚本
    echo 3. 检查防病毒软件是否阻止了程序运行
    echo 4. 运行 diagnose_simple.bat 进行系统诊断
    echo.
)

echo 按任意键退出...
pause >nul
goto :eof

:: =============================================================================
:: 辅助函数
:: =============================================================================

:log
echo %~1
echo %~1 >> "%LOG_FILE%"
goto :eof

:error_exit
call :log "致命错误: %~1"
echo.
echo 启动失败: %~1
echo 详细信息请查看日志: %LOG_FILE%
echo.
echo 建议操作:
echo 1. 运行 diagnose_simple.bat 进行系统诊断
echo 2. 检查所有依赖文件是否完整
echo 3. 以管理员身份重新运行
echo.
echo 按任意键退出...
pause >nul
exit /b 1

:add_to_path
:: 安全地添加目录到PATH，避免重复和过长问题
echo %PATH% | findstr /i "%~1" >nul
if !errorlevel! neq 0 (
    set "PATH=%~1;%PATH%"
    call :log "已添加到PATH: %~1"
)
goto :eof

:suggest_vcredist
call :log "建议安装Microsoft Visual C++ Redistributable:"
call :log "  - 下载地址: https://aka.ms/vs/17/release/vc_redist.x64.exe"
call :log "  - 或搜索 'Microsoft Visual C++ Redistributable'"
goto :eof
