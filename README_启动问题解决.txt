===============================================
    NarratoAI 启动问题解决方案
===============================================

如果您的 start.bat 脚本出现闪退或无法正常启动的问题，
请按照以下步骤操作：

===============================================
快速解决方案
===============================================

1. 【推荐】双击运行：一键启动.bat
   - 这是最简单的解决方案
   - 包含菜单选项，可以诊断、修复和启动
   - 适合所有用户

2. 如果仍有问题，请按顺序运行：
   a) diagnose_simple.bat     (诊断问题)
   b) fix_common_issues.bat   (修复问题)
   c) start_improved.bat      (启动应用)

===============================================
文件说明
===============================================

一键启动.bat
- 综合解决方案，包含菜单界面
- 集成诊断、修复、启动功能
- 推荐首选使用

diagnose_simple.bat
- 系统诊断脚本
- 检查依赖项和配置
- 生成详细报告

fix_common_issues.bat
- 问题修复脚本
- 自动修复常见问题
- 清理缓存和配置

start_improved.bat
- 改进版启动脚本
- 增强错误处理
- 详细日志记录

启动问题解决方案.md
- 详细技术文档
- 问题分析和解决方案
- 故障排除指南

===============================================
常见问题
===============================================

Q: 脚本一闪就关闭了
A: 运行 diagnose_simple.bat 检查问题，
   然后运行 fix_common_issues.bat 修复

Q: 提示权限不足
A: 右键点击脚本，选择"以管理员身份运行"

Q: 提示端口被占用
A: 运行 fix_common_issues.bat 自动处理

Q: Python 或 Streamlit 错误
A: 运行 fix_common_issues.bat 自动安装

Q: 防病毒软件报警
A: 将 NarratoAI 目录添加到防病毒软件白名单

===============================================
技术支持
===============================================

如果按照上述步骤仍无法解决问题：

1. 保存以下日志文件：
   - diagnostic_report.txt
   - startup.log
   - fix_log.txt

2. 记录具体错误信息

3. 提供系统信息：
   - Windows 版本
   - 32位/64位
   - 防病毒软件类型

4. 联系技术支持并提供上述信息

===============================================
预防措施
===============================================

1. 不要移动或重命名 lib 目录
2. 确保有足够的磁盘空间
3. 定期运行 fix_common_issues.bat 清理缓存
4. 保持系统更新

===============================================

祝您使用愉快！
