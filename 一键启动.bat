@echo off
:: =============================================================================
:: NarratoAI 一键启动解决方案
:: 集成诊断、修复和启动功能的综合脚本
:: =============================================================================

title NarratoAI 一键启动解决方案
setlocal enabledelayedexpansion
chcp 65001 >nul 2>&1

set "SCRIPT_DIR=%~dp0"

:main_menu
cls
echo.
echo ===============================================
echo    NarratoAI 一键启动解决方案
echo ===============================================
echo.
echo 请选择操作：
echo.
echo [1] 直接启动 NarratoAI（推荐）
echo [2] 系统诊断（检查问题）
echo [3] 修复常见问题
echo [4] 查看帮助信息
echo [5] 退出
echo.
set /p choice=请输入选项 (1-5): 

if "%choice%"=="1" goto start_app
if "%choice%"=="2" goto diagnose
if "%choice%"=="3" goto fix_issues
if "%choice%"=="4" goto show_help
if "%choice%"=="5" goto exit
goto main_menu

:start_app
cls
echo ===============================================
echo    启动 NarratoAI 应用
echo ===============================================
echo.

:: 快速检查关键依赖
echo 正在进行快速检查...

set "ERROR_FOUND=0"

if not exist "%SCRIPT_DIR%NarratoAI\webui.py" (
    echo ✗ 错误: 主程序文件不存在
    set "ERROR_FOUND=1"
)

if not exist "%SCRIPT_DIR%lib\python\python.exe" (
    echo ✗ 错误: Python解释器不存在
    set "ERROR_FOUND=1"
)

if %ERROR_FOUND% equ 1 (
    echo.
    echo 发现关键文件缺失，建议先运行系统诊断。
    echo.
    echo [1] 返回主菜单
    echo [2] 强制启动
    set /p choice2=请选择: 
    if "!choice2!"=="1" goto main_menu
)

echo.
echo 正在启动应用...
echo 如果启动失败，请选择菜单中的"修复常见问题"选项
echo.

:: 调用改进版启动脚本
if exist "%SCRIPT_DIR%start_improved.bat" (
    call "%SCRIPT_DIR%start_improved.bat"
) else (
    echo 错误: 找不到启动脚本 start_improved.bat
    echo 请确保所有文件完整
    pause
)

goto main_menu

:diagnose
cls
echo ===============================================
echo    系统诊断
echo ===============================================
echo.

if exist "%SCRIPT_DIR%diagnose_simple.bat" (
    echo 正在运行系统诊断...
    echo.
    call "%SCRIPT_DIR%diagnose_simple.bat"
) else (
    echo 错误: 找不到诊断脚本 diagnose_simple.bat
    echo 请确保所有文件完整
    pause
)

echo.
echo 诊断完成，按任意键返回主菜单...
pause >nul
goto main_menu

:fix_issues
cls
echo ===============================================
echo    修复常见问题
echo ===============================================
echo.

if exist "%SCRIPT_DIR%fix_common_issues.bat" (
    echo 正在修复常见问题...
    echo.
    call "%SCRIPT_DIR%fix_common_issues.bat"
) else (
    echo 错误: 找不到修复脚本 fix_common_issues.bat
    echo 请确保所有文件完整
    pause
)

echo.
echo 修复完成，按任意键返回主菜单...
pause >nul
goto main_menu

:show_help
cls
echo ===============================================
echo    帮助信息
echo ===============================================
echo.
echo 脚本说明：
echo.
echo 1. 直接启动：
echo    - 使用改进版启动脚本
echo    - 包含错误检查和详细日志
echo    - 推荐首选方式
echo.
echo 2. 系统诊断：
echo    - 检查所有依赖项和配置
echo    - 生成详细诊断报告
echo    - 识别潜在问题
echo.
echo 3. 修复常见问题：
echo    - 自动修复配置问题
echo    - 清理缓存和临时文件
echo    - 安装缺失的模块
echo.
echo 常见问题解决：
echo.
echo 问题：脚本闪退
echo 解决：先运行"系统诊断"，然后"修复常见问题"
echo.
echo 问题：权限不足
echo 解决：右键以管理员身份运行此脚本
echo.
echo 问题：端口被占用
echo 解决：运行"修复常见问题"自动处理
echo.
echo 问题：Python模块缺失
echo 解决：运行"修复常见问题"自动安装
echo.
echo 技术支持：
echo - 查看生成的日志文件获取详细信息
echo - 确保防病毒软件不阻止程序运行
echo - 安装 Microsoft Visual C++ Redistributable
echo.
echo 按任意键返回主菜单...
pause >nul
goto main_menu

:exit
echo.
echo 感谢使用 NarratoAI！
echo.
timeout /t 2 >nul
exit /b 0
