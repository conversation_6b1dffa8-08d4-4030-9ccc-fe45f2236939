@echo off
setlocal enabledelayedexpansion

:main_menu
cls
echo.
echo ===============================================
echo    NarratoAI 启动菜单
echo ===============================================
echo.
echo 请选择操作：
echo.
echo [1] 启动 NarratoAI
echo [2] 系统检查
echo [3] 修复问题
echo [4] 查看帮助
echo [5] 退出
echo.
set /p choice=请输入选项 (1-5): 

if "%choice%"=="1" goto start_app
if "%choice%"=="2" goto check_system
if "%choice%"=="3" goto fix_issues
if "%choice%"=="4" goto show_help
if "%choice%"=="5" goto exit_menu
echo 无效选项，请重新选择
timeout /t 2 >nul
goto main_menu

:start_app
cls
echo ===============================================
echo    启动 NarratoAI
echo ===============================================
echo.
if exist "%~dp0start_safe.bat" (
    call "%~dp0start_safe.bat"
) else (
    echo 错误: 找不到启动脚本 start_safe.bat
    pause
)
goto main_menu

:check_system
cls
echo ===============================================
echo    系统检查
echo ===============================================
echo.
if exist "%~dp0check_system.bat" (
    call "%~dp0check_system.bat"
) else (
    echo 错误: 找不到检查脚本 check_system.bat
    pause
)
goto main_menu

:fix_issues
cls
echo ===============================================
echo    修复问题
echo ===============================================
echo.
if exist "%~dp0fix_issues.bat" (
    call "%~dp0fix_issues.bat"
) else (
    echo 错误: 找不到修复脚本 fix_issues.bat
    pause
)
goto main_menu

:show_help
cls
echo ===============================================
echo    帮助信息
echo ===============================================
echo.
echo 脚本说明：
echo.
echo 1. 启动 NarratoAI
echo    - 使用安全启动脚本
echo    - 自动检查依赖项
echo    - 详细错误日志
echo.
echo 2. 系统检查
echo    - 检查所有依赖项
echo    - 生成检查报告
echo    - 识别潜在问题
echo.
echo 3. 修复问题
echo    - 自动修复常见问题
echo    - 清理缓存文件
echo    - 安装缺失模块
echo.
echo 常见问题：
echo.
echo Q: 脚本闪退怎么办？
echo A: 先运行"系统检查"，然后"修复问题"
echo.
echo Q: 提示权限不足？
echo A: 右键以管理员身份运行
echo.
echo Q: 端口被占用？
echo A: 运行"修复问题"自动处理
echo.
echo Q: 缺少模块？
echo A: 运行"修复问题"自动安装
echo.
echo 技术支持：
echo - 查看生成的日志文件
echo - 确保防病毒软件不阻止运行
echo - 安装 Visual C++ Redistributable
echo.
echo 按任意键返回主菜单...
pause >nul
goto main_menu

:exit_menu
echo.
echo 感谢使用 NarratoAI！
echo.
timeout /t 2 >nul
exit /b 0
