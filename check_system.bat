@echo off
setlocal enabledelayedexpansion

echo ===============================================
echo    NarratoAI 系统检查工具
echo ===============================================
echo.

set "SCRIPT_DIR=%~dp0"
set "LOG_FILE=%SCRIPT_DIR%check_report.txt"
set ERROR_COUNT=0

echo 系统检查报告 > "%LOG_FILE%"
echo 检查时间: %date% %time% >> "%LOG_FILE%"
echo ============================================== >> "%LOG_FILE%"

echo 正在检查系统环境...
echo.

:: 1. 检查目录结构
echo [1/6] 检查目录结构...
echo [1/6] 检查目录结构... >> "%LOG_FILE%"

if exist "%SCRIPT_DIR%NarratoAI" (
    echo √ NarratoAI目录存在
    echo √ NarratoAI目录存在 >> "%LOG_FILE%"
) else (
    echo × NarratoAI目录不存在
    echo × NarratoAI目录不存在 >> "%LOG_FILE%"
    set /a ERROR_COUNT+=1
)

if exist "%SCRIPT_DIR%lib" (
    echo √ lib目录存在
    echo √ lib目录存在 >> "%LOG_FILE%"
) else (
    echo × lib目录不存在
    echo × lib目录不存在 >> "%LOG_FILE%"
    set /a ERROR_COUNT+=1
)

:: 2. 检查Python
echo.
echo [2/6] 检查Python环境...
echo [2/6] 检查Python环境... >> "%LOG_FILE%"

if exist "%SCRIPT_DIR%lib\python\python.exe" (
    echo √ Python解释器存在
    echo √ Python解释器存在 >> "%LOG_FILE%"
    
    "%SCRIPT_DIR%lib\python\python.exe" --version >nul 2>&1
    if !errorlevel! equ 0 (
        echo √ Python可以运行
        echo √ Python可以运行 >> "%LOG_FILE%"
    ) else (
        echo × Python无法运行
        echo × Python无法运行 >> "%LOG_FILE%"
        set /a ERROR_COUNT+=1
    )
) else (
    echo × Python解释器不存在
    echo × Python解释器不存在 >> "%LOG_FILE%"
    set /a ERROR_COUNT+=1
)

:: 3. 检查FFmpeg
echo.
echo [3/6] 检查FFmpeg...
echo [3/6] 检查FFmpeg... >> "%LOG_FILE%"

if exist "%SCRIPT_DIR%lib\ffmpeg\ffmpeg-7.0-essentials_build\ffmpeg.exe" (
    echo √ FFmpeg存在
    echo √ FFmpeg存在 >> "%LOG_FILE%"
) else (
    echo × FFmpeg不存在
    echo × FFmpeg不存在 >> "%LOG_FILE%"
    set /a ERROR_COUNT+=1
)

:: 4. 检查ImageMagick
echo.
echo [4/6] 检查ImageMagick...
echo [4/6] 检查ImageMagick... >> "%LOG_FILE%"

if exist "%SCRIPT_DIR%lib\imagemagic\ImageMagick-7.1.1-29-portable-Q16-x64\magick.exe" (
    echo √ ImageMagick存在
    echo √ ImageMagick存在 >> "%LOG_FILE%"
) else (
    echo × ImageMagick不存在
    echo × ImageMagick不存在 >> "%LOG_FILE%"
    set /a ERROR_COUNT+=1
)

:: 5. 检查主程序
echo.
echo [5/6] 检查主程序文件...
echo [5/6] 检查主程序文件... >> "%LOG_FILE%"

if exist "%SCRIPT_DIR%NarratoAI\webui.py" (
    echo √ webui.py存在
    echo √ webui.py存在 >> "%LOG_FILE%"
) else (
    echo × webui.py不存在
    echo × webui.py不存在 >> "%LOG_FILE%"
    set /a ERROR_COUNT+=1
)

:: 6. 检查权限
echo.
echo [6/6] 检查文件权限...
echo [6/6] 检查文件权限... >> "%LOG_FILE%"

echo test > "%SCRIPT_DIR%temp_test.txt" 2>nul
if exist "%SCRIPT_DIR%temp_test.txt" (
    echo √ 具有写入权限
    echo √ 具有写入权限 >> "%LOG_FILE%"
    del "%SCRIPT_DIR%temp_test.txt" 2>nul
) else (
    echo × 缺少写入权限
    echo × 缺少写入权限 >> "%LOG_FILE%"
    set /a ERROR_COUNT+=1
)

:: 显示结果
echo.
echo ==============================================
echo 检查完成！
echo ==============================================
echo.

echo 检查完成！ >> "%LOG_FILE%"
echo 错误数量: %ERROR_COUNT% >> "%LOG_FILE%"

if %ERROR_COUNT% equ 0 (
    echo √ 未发现错误，系统应该可以正常运行
    echo √ 未发现错误，系统应该可以正常运行 >> "%LOG_FILE%"
    echo.
    echo 如果仍有问题，请：
    echo 1. 以管理员身份运行脚本
    echo 2. 检查防病毒软件设置
    echo 3. 确保网络连接正常
) else (
    echo × 发现 %ERROR_COUNT% 个错误
    echo × 发现 %ERROR_COUNT% 个错误 >> "%LOG_FILE%"
    echo.
    echo 请修复上述错误后重试
    echo 建议运行 fix_issues.bat 自动修复
)

echo.
echo 详细报告保存在: %LOG_FILE%
echo.
echo 按任意键退出...
pause >nul
