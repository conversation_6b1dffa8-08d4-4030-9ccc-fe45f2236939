# # User controllable configuration parameters for docx2txt.pl
#
# Note:
#     - Ensure that all configuration lines end with single comma (,).
#     - Lines beginning with '#' are comments.
#

#
# Specify the path to "unzip" command.
#
# Windows users should specify this path like
#
# 'C:\Cygwin\bin\unzip.exe'	(With Windows native perl.exe)
# Or
# 'C:/Cygwin/bin/unzip.exe'	(With Cygwin/Windows native perl.exe)
#
# Default : '/usr/bin/unzip'
#
config_unzip         => '/usr/bin/unzip',

#
# Specify the commandline option(s) to be supplied to the program specified in
# config_unzip, that allow silent extraction of specified file from zip archive
# to console/standard output/pipe.
#
# Default : '-p' (for unzip)
#
# config_unzip_opts         => '-p',

#
# How the newline should be in output text file - "\n" or "\r\n".
#
# Default : "\n"
#
# config_newLine       => "\n",

#
# Line width to use for short line (single line paragraph) justifiction.
#
# Default : 80
#
# config_lineWidth     => 80,

#
# Show hyperlink alongside linked text - [yY/nN]
#
# Note: Even if this option is enabled, hyperlinks will be shown only if
#       hyperlink differs from the linked text.
#
# Default : N
#
config_showHyperLink => "Y",

#
# Directory for creation of temporary file.
#
# In case input is provided via STDIN (standard input), it is first stored in a
# temporary file and then further processing is done using that file.
# Same path specification rules apply here as in case of config_unzip.
#
# Default : "/tmp" (Non-Windows OSes), Environment variable "TEMP" (Windows)
#
# config_tempDir => "/path/to/your/temp/directory",

#
# Approximate mapping of twips to character for layout purpose.
#
# Default : 120
#
config_twipsPerChar => 240,
