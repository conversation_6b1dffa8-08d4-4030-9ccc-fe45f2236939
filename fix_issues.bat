@echo off
setlocal enabledelayedexpansion

echo ===============================================
echo    NarratoAI 问题修复工具
echo ===============================================
echo.

set "SCRIPT_DIR=%~dp0"
set "LOG_FILE=%SCRIPT_DIR%fix_report.txt"

echo 修复操作报告 > "%LOG_FILE%"
echo 修复时间: %date% %time% >> "%LOG_FILE%"
echo ============================================== >> "%LOG_FILE%"

echo 正在修复常见问题...
echo.

:: 1. 修复配置文件
echo [1/5] 检查配置文件...
echo [1/5] 检查配置文件... >> "%LOG_FILE%"

if not exist "%SCRIPT_DIR%NarratoAI\config.toml" (
    if exist "%SCRIPT_DIR%NarratoAI\config.example.toml" (
        echo 复制配置文件...
        copy "%SCRIPT_DIR%NarratoAI\config.example.toml" "%SCRIPT_DIR%NarratoAI\config.toml" >nul 2>&1
        if !errorlevel! equ 0 (
            echo √ 配置文件已创建
            echo √ 配置文件已创建 >> "%LOG_FILE%"
        ) else (
            echo × 无法创建配置文件
            echo × 无法创建配置文件 >> "%LOG_FILE%"
        )
    ) else (
        echo ! 示例配置文件不存在
        echo ! 示例配置文件不存在 >> "%LOG_FILE%"
    )
) else (
    echo √ 配置文件已存在
    echo √ 配置文件已存在 >> "%LOG_FILE%"
)

:: 2. 创建Streamlit目录
echo.
echo [2/5] 修复Streamlit配置...
echo [2/5] 修复Streamlit配置... >> "%LOG_FILE%"

set "STREAMLIT_DIR=%USERPROFILE%\.streamlit"
if not exist "%STREAMLIT_DIR%" (
    mkdir "%STREAMLIT_DIR%" 2>nul
    if !errorlevel! equ 0 (
        echo √ Streamlit目录已创建
        echo √ Streamlit目录已创建 >> "%LOG_FILE%"
    ) else (
        echo × 无法创建Streamlit目录
        echo × 无法创建Streamlit目录 >> "%LOG_FILE%"
    )
) else (
    echo √ Streamlit目录已存在
    echo √ Streamlit目录已存在 >> "%LOG_FILE%"
)

:: 创建凭证文件
set "CREDENTIAL_FILE=%STREAMLIT_DIR%\credentials.toml"
if not exist "%CREDENTIAL_FILE%" (
    (
        echo [general]
        echo email = ""
    ) > "%CREDENTIAL_FILE%" 2>nul
    if !errorlevel! equ 0 (
        echo √ Streamlit凭证文件已创建
        echo √ Streamlit凭证文件已创建 >> "%LOG_FILE%"
    )
)

:: 3. 检查Python模块
echo.
echo [3/5] 检查Python模块...
echo [3/5] 检查Python模块... >> "%LOG_FILE%"

if exist "%SCRIPT_DIR%lib\python\python.exe" (
    "%SCRIPT_DIR%lib\python\python.exe" -c "import streamlit" >nul 2>&1
    if !errorlevel! neq 0 (
        echo 尝试安装Streamlit...
        echo 尝试安装Streamlit... >> "%LOG_FILE%"
        "%SCRIPT_DIR%lib\python\python.exe" -m pip install streamlit --quiet 2>>"%LOG_FILE%"
        if !errorlevel! equ 0 (
            echo √ Streamlit安装成功
            echo √ Streamlit安装成功 >> "%LOG_FILE%"
        ) else (
            echo × Streamlit安装失败
            echo × Streamlit安装失败 >> "%LOG_FILE%"
        )
    ) else (
        echo √ Streamlit模块可用
        echo √ Streamlit模块可用 >> "%LOG_FILE%"
    )
) else (
    echo × Python解释器不存在
    echo × Python解释器不存在 >> "%LOG_FILE%"
)

:: 4. 清理临时文件
echo.
echo [4/5] 清理临时文件...
echo [4/5] 清理临时文件... >> "%LOG_FILE%"

:: 清理Python缓存
for /d %%d in ("%SCRIPT_DIR%NarratoAI\*__pycache__*") do (
    if exist "%%d" (
        rmdir /s /q "%%d" 2>nul
        echo √ 清理Python缓存: %%~nxd
        echo √ 清理Python缓存: %%~nxd >> "%LOG_FILE%"
    )
)

:: 清理临时文件
for %%f in ("%SCRIPT_DIR%*.tmp" "%SCRIPT_DIR%*.temp") do (
    if exist "%%f" (
        del "%%f" 2>nul
        echo √ 删除临时文件: %%~nxf
        echo √ 删除临时文件: %%~nxf >> "%LOG_FILE%"
    )
)

:: 5. 检查端口占用
echo.
echo [5/5] 检查端口占用...
echo [5/5] 检查端口占用... >> "%LOG_FILE%"

netstat -an 2>nul | findstr ":8501" >nul
if !errorlevel! equ 0 (
    echo ! 端口8501被占用
    echo ! 端口8501被占用 >> "%LOG_FILE%"
    echo 尝试结束占用进程...
    
    for /f "tokens=5" %%a in ('netstat -ano 2^>nul ^| findstr ":8501"') do (
        taskkill /pid %%a /f >nul 2>&1
        if !errorlevel! equ 0 (
            echo √ 结束进程 PID: %%a
            echo √ 结束进程 PID: %%a >> "%LOG_FILE%"
        )
    )
) else (
    echo √ 端口8501可用
    echo √ 端口8501可用 >> "%LOG_FILE%"
)

:: 显示结果
echo.
echo ==============================================
echo 修复完成！
echo ==============================================
echo.

echo 修复完成！ >> "%LOG_FILE%"

echo 建议操作：
echo 1. 重新运行启动脚本
echo 2. 如果仍有问题，以管理员身份运行
echo 3. 检查防病毒软件设置
echo.
echo 修复报告保存在: %LOG_FILE%
echo.
echo 按任意键退出...
pause >nul
