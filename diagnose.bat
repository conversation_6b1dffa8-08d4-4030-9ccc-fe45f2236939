@echo off
:: =============================================================================
:: NarratoAI 系统诊断脚本
:: 用于检测可能导致 start.bat 脚本闪退的问题
:: =============================================================================

:: 设置控制台标题
title NarratoAI 系统诊断工具

:: 创建日志文件
set "LOG_FILE=%~dp0diagnostic_report.txt"
echo NarratoAI 系统诊断报告 > "%LOG_FILE%"
echo 生成时间: %date% %time% >> "%LOG_FILE%"
echo ============================================== >> "%LOG_FILE%"

:: 定义输出函数（同时输出到屏幕和文件）
set "ECHO_BOTH=echo"

echo ===============================================
echo    NarratoAI 系统诊断工具
echo ===============================================
echo.
echo 正在检测系统环境和依赖项...
echo 诊断报告将保存到: %LOG_FILE%
echo.

:: 获取脚本目录
set "SCRIPT_DIR=%~dp0"
echo 脚本目录: %SCRIPT_DIR%
echo 脚本目录: %SCRIPT_DIR% >> "%LOG_FILE%"

:: =============================================================================
:: 1. 系统基本信息检查
:: =============================================================================
echo. | tee -a "%LOG_FILE%"
echo [1/10] 检查系统基本信息...
echo [1/10] 检查系统基本信息... >> "%LOG_FILE%"

echo 操作系统信息: >> "%LOG_FILE%"
ver >> "%LOG_FILE%"
echo 系统架构: >> "%LOG_FILE%"
echo %PROCESSOR_ARCHITECTURE% >> "%LOG_FILE%"
echo 用户名: %USERNAME% >> "%LOG_FILE%"
echo 当前代码页: >> "%LOG_FILE%"
chcp >> "%LOG_FILE%"

:: =============================================================================
:: 2. 目录结构检查
:: =============================================================================
echo. | tee -a "%LOG_FILE%"
echo [2/10] 检查目录结构... | tee -a "%LOG_FILE%"

set "ERRORS=0"

:: 检查主要目录
if exist "%SCRIPT_DIR%NarratoAI" (
    echo ✓ NarratoAI 目录存在 | tee -a "%LOG_FILE%"
) else (
    echo ✗ 错误: NarratoAI 目录不存在 | tee -a "%LOG_FILE%"
    set /a ERRORS+=1
)

if exist "%SCRIPT_DIR%lib" (
    echo ✓ lib 目录存在 | tee -a "%LOG_FILE%"
) else (
    echo ✗ 错误: lib 目录不存在 | tee -a "%LOG_FILE%"
    set /a ERRORS+=1
)

:: =============================================================================
:: 3. Python 环境检查
:: =============================================================================
echo. | tee -a "%LOG_FILE%"
echo [3/10] 检查 Python 环境... | tee -a "%LOG_FILE%"

set "PYTHON_EXE=%SCRIPT_DIR%lib\python\python.exe"
if exist "%PYTHON_EXE%" (
    echo ✓ Python 可执行文件存在: %PYTHON_EXE% | tee -a "%LOG_FILE%"
    
    :: 测试 Python 版本
    "%PYTHON_EXE%" --version >> "%LOG_FILE%" 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo ✓ Python 可以正常运行 | tee -a "%LOG_FILE%"
    ) else (
        echo ✗ 错误: Python 无法正常运行 | tee -a "%LOG_FILE%"
        set /a ERRORS+=1
    )
    
    :: 检查 Streamlit
    echo 检查 Streamlit 模块... | tee -a "%LOG_FILE%"
    "%PYTHON_EXE%" -c "import streamlit; print('Streamlit 版本:', streamlit.__version__)" >> "%LOG_FILE%" 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo ✓ Streamlit 模块可用 | tee -a "%LOG_FILE%"
    ) else (
        echo ✗ 错误: Streamlit 模块不可用 | tee -a "%LOG_FILE%"
        set /a ERRORS+=1
    )
) else (
    echo ✗ 错误: Python 可执行文件不存在: %PYTHON_EXE% | tee -a "%LOG_FILE%"
    set /a ERRORS+=1
)

:: =============================================================================
:: 4. FFmpeg 检查
:: =============================================================================
echo. | tee -a "%LOG_FILE%"
echo [4/10] 检查 FFmpeg... | tee -a "%LOG_FILE%"

set "FFMPEG_EXE=%SCRIPT_DIR%lib\ffmpeg\ffmpeg-7.0-essentials_build\ffmpeg.exe"
if exist "%FFMPEG_EXE%" (
    echo ✓ FFmpeg 可执行文件存在: %FFMPEG_EXE% | tee -a "%LOG_FILE%"
    
    :: 测试 FFmpeg 版本
    "%FFMPEG_EXE%" -version 2>&1 | findstr "ffmpeg version" >> "%LOG_FILE%"
    if %ERRORLEVEL% EQU 0 (
        echo ✓ FFmpeg 可以正常运行 | tee -a "%LOG_FILE%"
    ) else (
        echo ✗ 警告: FFmpeg 可能无法正常运行 | tee -a "%LOG_FILE%"
    )
) else (
    echo ✗ 错误: FFmpeg 可执行文件不存在: %FFMPEG_EXE% | tee -a "%LOG_FILE%"
    set /a ERRORS+=1
)

:: =============================================================================
:: 5. ImageMagick 检查
:: =============================================================================
echo. | tee -a "%LOG_FILE%"
echo [5/10] 检查 ImageMagick... | tee -a "%LOG_FILE%"

set "MAGICK_EXE=%SCRIPT_DIR%lib\imagemagic\ImageMagick-7.1.1-29-portable-Q16-x64\magick.exe"
if exist "%MAGICK_EXE%" (
    echo ✓ ImageMagick 可执行文件存在: %MAGICK_EXE% | tee -a "%LOG_FILE%"
    
    :: 测试 ImageMagick 版本
    "%MAGICK_EXE%" -version 2>&1 | findstr "Version:" >> "%LOG_FILE%"
    if %ERRORLEVEL% EQU 0 (
        echo ✓ ImageMagick 可以正常运行 | tee -a "%LOG_FILE%"
    ) else (
        echo ✗ 警告: ImageMagick 可能无法正常运行 | tee -a "%LOG_FILE%"
    )
) else (
    echo ✗ 错误: ImageMagick 可执行文件不存在: %MAGICK_EXE% | tee -a "%LOG_FILE%"
    set /a ERRORS+=1
)

:: =============================================================================
:: 6. 关键文件检查
:: =============================================================================
echo. | tee -a "%LOG_FILE%"
echo [6/10] 检查关键文件... | tee -a "%LOG_FILE%"

if exist "%SCRIPT_DIR%NarratoAI\webui.py" (
    echo ✓ webui.py 存在 | tee -a "%LOG_FILE%"
) else (
    echo ✗ 错误: webui.py 不存在 | tee -a "%LOG_FILE%"
    set /a ERRORS+=1
)

if exist "%SCRIPT_DIR%NarratoAI\config.toml" (
    echo ✓ config.toml 存在 | tee -a "%LOG_FILE%"
) else (
    echo ✗ 警告: config.toml 不存在，可能需要从 config.example.toml 复制 | tee -a "%LOG_FILE%"
)

:: =============================================================================
:: 7. 权限检查
:: =============================================================================
echo. | tee -a "%LOG_FILE%"
echo [7/10] 检查文件权限... | tee -a "%LOG_FILE%"

:: 测试创建临时文件的权限
set "TEMP_FILE=%SCRIPT_DIR%temp_test.txt"
echo test > "%TEMP_FILE%" 2>nul
if exist "%TEMP_FILE%" (
    echo ✓ 具有写入权限 | tee -a "%LOG_FILE%"
    del "%TEMP_FILE%" 2>nul
) else (
    echo ✗ 错误: 缺少写入权限 | tee -a "%LOG_FILE%"
    set /a ERRORS+=1
)

:: 检查 Streamlit 目录权限
set "STREAMLIT_DIR=%USERPROFILE%\.streamlit"
if not exist "%STREAMLIT_DIR%" (
    mkdir "%STREAMLIT_DIR%" 2>nul
    if exist "%STREAMLIT_DIR%" (
        echo ✓ 可以创建 Streamlit 配置目录 | tee -a "%LOG_FILE%"
    ) else (
        echo ✗ 错误: 无法创建 Streamlit 配置目录 | tee -a "%LOG_FILE%"
        set /a ERRORS+=1
    )
) else (
    echo ✓ Streamlit 配置目录已存在 | tee -a "%LOG_FILE%"
)

:: =============================================================================
:: 8. 环境变量检查
:: =============================================================================
echo. | tee -a "%LOG_FILE%"
echo [8/10] 检查环境变量... | tee -a "%LOG_FILE%"

echo PATH 长度: >> "%LOG_FILE%"
echo %PATH% | find /c ";" >> "%LOG_FILE%"

if defined PYTHONPATH (
    echo PYTHONPATH 已设置: %PYTHONPATH% >> "%LOG_FILE%"
) else (
    echo PYTHONPATH 未设置 >> "%LOG_FILE%"
)

:: =============================================================================
:: 9. 网络和端口检查
:: =============================================================================
echo. | tee -a "%LOG_FILE%"
echo [9/10] 检查网络和端口... | tee -a "%LOG_FILE%"

:: 检查 8501 端口是否被占用（Streamlit 默认端口）
netstat -an | findstr ":8501" >> "%LOG_FILE%" 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✗ 警告: 端口 8501 可能被占用 | tee -a "%LOG_FILE%"
) else (
    echo ✓ 端口 8501 可用 | tee -a "%LOG_FILE%"
)

:: =============================================================================
:: 10. 系统兼容性检查
:: =============================================================================
echo. | tee -a "%LOG_FILE%"
echo [10/10] 检查系统兼容性... | tee -a "%LOG_FILE%"

:: 检查 Visual C++ 运行时
echo 检查 Visual C++ 运行时... | tee -a "%LOG_FILE%"
reg query "HKLM\SOFTWARE\Microsoft\VisualStudio\14.0\VC\Runtimes\x64" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ Visual C++ 2015-2022 运行时已安装 | tee -a "%LOG_FILE%"
) else (
    echo ✗ 警告: 可能缺少 Visual C++ 运行时 | tee -a "%LOG_FILE%"
)

:: =============================================================================
:: 诊断结果汇总
:: =============================================================================
echo. | tee -a "%LOG_FILE%"
echo ============================================== | tee -a "%LOG_FILE%"
echo 诊断完成！ | tee -a "%LOG_FILE%"
echo. | tee -a "%LOG_FILE%"

if %ERRORS% EQU 0 (
    echo ✓ 未发现严重错误，系统应该可以正常运行 | tee -a "%LOG_FILE%"
    echo 如果仍然遇到问题，请检查防病毒软件设置 | tee -a "%LOG_FILE%"
) else (
    echo ✗ 发现 %ERRORS% 个错误，需要修复后才能正常运行 | tee -a "%LOG_FILE%"
    echo 请查看上述错误信息并进行相应修复 | tee -a "%LOG_FILE%"
)

echo. | tee -a "%LOG_FILE%"
echo 详细报告已保存到: %LOG_FILE% | tee -a "%LOG_FILE%"
echo.
echo 按任意键退出...
pause >nul
