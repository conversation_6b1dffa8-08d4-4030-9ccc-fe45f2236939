===============================================
    NarratoAI 启动解决方案
===============================================

如果 start.bat 脚本闪退，请使用以下解决方案：

===============================================
快速开始
===============================================

双击运行：simple_menu.bat
然后按照菜单提示操作即可

===============================================
文件说明
===============================================

simple_menu.bat     - 主菜单（推荐使用）
simple_check.bat    - 系统检查
simple_fix.bat      - 问题修复  
simple_start.bat    - 安全启动
start.bat           - 原始启动脚本

===============================================
手动操作步骤
===============================================

1. 运行 simple_check.bat 检查系统
2. 运行 simple_fix.bat 修复问题
3. 运行 simple_start.bat 启动应用

===============================================
常见问题
===============================================

Q: 脚本闪退？
A: 运行检查和修复脚本

Q: 权限不足？  
A: 右键"以管理员身份运行"

Q: 端口被占用？
A: 运行修复脚本自动处理

===============================================

详细说明请查看：解决方案说明.txt
