===============================================
    NarratoAI 启动问题解决方案
===============================================

如果您的 start.bat 脚本出现闪退问题，请使用以下解决方案：

===============================================
推荐使用方法
===============================================

方法一：使用菜单（推荐）
1. 双击运行 menu.bat
2. 选择相应的操作选项
3. 按照提示进行操作

方法二：手动运行
1. 双击 check_system.bat 检查系统
2. 双击 fix_issues.bat 修复问题  
3. 双击 start_safe.bat 启动应用

===============================================
文件说明
===============================================

menu.bat
- 主菜单脚本，提供友好界面
- 集成所有功能，推荐使用

check_system.bat  
- 系统检查脚本
- 检查6个关键项目
- 生成详细检查报告

fix_issues.bat
- 问题修复脚本  
- 修复5类常见问题
- 自动清理和配置

start_safe.bat
- 安全启动脚本
- 增强错误处理
- 详细启动日志

===============================================
常见问题解决
===============================================

问题：脚本一闪就关闭
解决：
1. 运行 check_system.bat 检查问题
2. 运行 fix_issues.bat 修复问题
3. 使用 start_safe.bat 启动

问题：提示权限不足
解决：
1. 右键点击脚本
2. 选择"以管理员身份运行"

问题：提示缺少文件
解决：
1. 检查 lib 目录是否完整
2. 重新解压安装包
3. 确保所有文件都存在

问题：Python 相关错误
解决：
1. 运行 fix_issues.bat 自动修复
2. 手动安装 Visual C++ Redistributable
3. 检查防病毒软件设置

问题：端口被占用
解决：
1. 运行 fix_issues.bat 自动处理
2. 手动结束占用 8501 端口的进程
3. 重启计算机

问题：防病毒软件报警
解决：
1. 将 NarratoAI 目录添加到白名单
2. 临时关闭实时保护
3. 信任相关可执行文件

===============================================
日志文件说明
===============================================

check_report.txt - 系统检查详细报告
fix_report.txt - 问题修复操作记录  
startup.log - 应用启动详细日志

这些日志文件包含详细的操作信息，
如果问题仍然存在，请查看相应日志。

===============================================
系统要求
===============================================

操作系统：Windows 7/8/10/11
架构：支持 32位 和 64位
运行时：Microsoft Visual C++ Redistributable
网络：需要网络连接（首次安装模块时）
权限：建议以管理员身份运行

===============================================
技术支持
===============================================

如果按照上述方法仍无法解决问题：

1. 保存所有日志文件
2. 记录具体错误信息  
3. 提供系统环境信息
4. 联系技术支持

常用下载链接：
Visual C++ Redistributable:
https://aka.ms/vs/17/release/vc_redist.x64.exe

===============================================

祝您使用愉快！
