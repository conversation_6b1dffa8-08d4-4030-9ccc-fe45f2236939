[app]
project_version = "0.6.7"
vision_llm_provider = "gemini"
vision_gemini_api_key = "sk-APnQX6tsB6lqElYVHPV6GD2BDXFXM8vwgsDCwCoJlmh7_0Sv"
vision_gemini_model_name = "gemini-2.0-flash-lite"
vision_qwenvl_api_key = ""
vision_qwenvl_model_name = "qwen2.5-vl-32b-instruct"
vision_qwenvl_base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
vision_siliconflow_api_key = ""
vision_siliconflow_model_name = "Qwen/Qwen2.5-VL-32B-Instruct"
vision_siliconflow_base_url = "https://api.siliconflow.cn/v1"
vision_openai_api_key = ""
vision_openai_model_name = "gpt-4.1-nano-2025-04-14"
vision_openai_base_url = "https://api.openai.com/v1"
narrato_api_key = ""
narrato_api_url = ""
narrato_model = "narra-1.0-2025-05-09"
text_llm_provider = "gemini"
text_openai_api_key = ""
text_openai_base_url = "https://api.openai.com/v1"
text_openai_model_name = "gpt-4.1-mini-2025-04-14"
text_siliconflow_api_key = ""
text_siliconflow_base_url = "https://api.siliconflow.cn/v1"
text_siliconflow_model_name = "deepseek-ai/DeepSeek-R1"
text_deepseek_api_key = ""
text_deepseek_base_url = "https://api.deepseek.com"
text_deepseek_model_name = "deepseek-chat"
text_gemini_api_key = "sk-APnQX6tsB6lqElYVHPV6GD2BDXFXM8vwgsDCwCoJlmh7_0Sv"
text_gemini_model_name = "gemini-2.0-flash"
text_gemini_base_url = "https://proxy.narratoai.cn/v1beta"
text_qwen_api_key = ""
text_qwen_model_name = "qwen-plus-1127"
text_qwen_base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
text_moonshot_api_key = ""
text_moonshot_base_url = "https://api.moonshot.cn/v1"
text_moonshot_model_name = "moonshot-v1-8k"
hide_config = true
vision_gemini_base_url = "https://proxy.narratoai.cn/v1beta"
video_clip_json_path = "E:\\NarratoAI_039\\NarratoAI_v0.6\\NarratoAI\\resource\\scripts\\2025-0712-125146.json"

[proxy]
http = ""
https = ""
enabled = false

[frames]
frame_interval_input = 3
vision_batch_size = 10

[azure]
speech_region = "eastus"
speech_key = "2vVtzA7OCTbkHLqMjvnNIRMIabVQCzOM1qrzuNmhsb5nSg5mTiCOJQQJ99BEACYeBjFXJ3w3AAAYACOGzHUN"

[ui]
language = "zh"
voice_name = "zh-CN-XiaoxiaoMultilingualNeural"
font_name = "MicrosoftYaHeiBold.ttc"
text_fore_color = "#FFFFFF"
font_size = 60
tts_engine = "azure_speech"
edge_voice_name = "zh-CN-XiaoxiaoNeural-Female"
edge_volume = 80
edge_rate = 1.0
edge_pitch = 0
azure_voice_name = "zh-CN-XiaoxiaoMultilingualNeural"
azure_volume = 80
azure_rate = 1.0
azure_pitch = 0

[soulvoice]
api_key = ""
voice_uri = "speech:mcg3fdnx:clzkyf4vy00e5qr6hywum4u84:bzznlkuhcjzpbosexitr"
model = "FunAudioLLM/CosyVoice2-0.5B"
api_url = "https://tts.scsmtech.cn/tts"
