@echo off
setlocal enabledelayedexpansion

echo ===============================================
echo    NarratoAI 安全启动脚本
echo ===============================================
echo.

set "CURRENT_DIR=%~dp0"
set "LOG_FILE=%CURRENT_DIR%startup.log"

echo NarratoAI 启动日志 > "%LOG_FILE%"
echo 启动时间: %date% %time% >> "%LOG_FILE%"
echo ============================================== >> "%LOG_FILE%"

echo 当前工作目录: %CURRENT_DIR%
echo 当前工作目录: %CURRENT_DIR% >> "%LOG_FILE%"

:: 检查基本目录结构
echo.
echo 检查系统环境...
echo 检查系统环境... >> "%LOG_FILE%"

if not exist "%CURRENT_DIR%NarratoAI" (
    echo 错误: 未找到NarratoAI目录
    echo 错误: 未找到NarratoAI目录 >> "%LOG_FILE%"
    goto error_exit
)

if not exist "%CURRENT_DIR%lib" (
    echo 错误: 未找到lib目录
    echo 错误: 未找到lib目录 >> "%LOG_FILE%"
    goto error_exit
)

:: 配置环境变量
echo.
echo 配置环境变量...
echo 配置环境变量... >> "%LOG_FILE%"

set "FFMPEG_BINARY=%CURRENT_DIR%lib\ffmpeg\ffmpeg-7.0-essentials_build\ffmpeg.exe"
set "FFMPEG_PATH=%CURRENT_DIR%lib\ffmpeg\ffmpeg-7.0-essentials_build"
set "IMAGEMAGICK_BINARY=%CURRENT_DIR%lib\imagemagic\ImageMagick-7.1.1-29-portable-Q16-x64\magick.exe"
set "IMAGEMAGICK_PATH=%CURRENT_DIR%lib\imagemagic\ImageMagick-7.1.1-29-portable-Q16-x64"
set "PYTHON_BINARY=%CURRENT_DIR%lib\python\python.exe"
set "PYTHONPATH=%CURRENT_DIR%NarratoAI;%PYTHONPATH%"

:: 安全地添加到PATH
echo %PATH% | findstr /i "%FFMPEG_PATH%" >nul
if !errorlevel! neq 0 (
    set "PATH=%FFMPEG_PATH%;%PATH%"
    echo 已添加FFmpeg到PATH >> "%LOG_FILE%"
)

echo %PATH% | findstr /i "%IMAGEMAGICK_PATH%" >nul
if !errorlevel! neq 0 (
    set "PATH=%IMAGEMAGICK_PATH%;%PATH%"
    echo 已添加ImageMagick到PATH >> "%LOG_FILE%"
)

:: 设置项目环境变量
set "NARRATO_ROOT=%CURRENT_DIR%NarratoAI"
set "NARRATO_FFMPEG=%FFMPEG_BINARY%"
set "NARRATO_IMAGEMAGICK=%IMAGEMAGICK_BINARY%"

:: 检查依赖项
echo.
echo 检查依赖项...
echo 检查依赖项... >> "%LOG_FILE%"

if not exist "%PYTHON_BINARY%" (
    echo 错误: Python解释器不存在: %PYTHON_BINARY%
    echo 错误: Python解释器不存在: %PYTHON_BINARY% >> "%LOG_FILE%"
    goto error_exit
)

if not exist "%FFMPEG_BINARY%" (
    echo 错误: FFmpeg不存在: %FFMPEG_BINARY%
    echo 错误: FFmpeg不存在: %FFMPEG_BINARY% >> "%LOG_FILE%"
    goto error_exit
)

if not exist "%IMAGEMAGICK_BINARY%" (
    echo 错误: ImageMagick不存在: %IMAGEMAGICK_BINARY%
    echo 错误: ImageMagick不存在: %IMAGEMAGICK_BINARY% >> "%LOG_FILE%"
    goto error_exit
)

if not exist "%CURRENT_DIR%NarratoAI\webui.py" (
    echo 错误: 主程序文件webui.py不存在
    echo 错误: 主程序文件webui.py不存在 >> "%LOG_FILE%"
    goto error_exit
)

echo √ 所有依赖项检查通过
echo √ 所有依赖项检查通过 >> "%LOG_FILE%"

:: 测试Python环境
echo.
echo 测试Python环境...
echo 测试Python环境... >> "%LOG_FILE%"

"%PYTHON_BINARY%" --version >nul 2>&1
if !errorlevel! neq 0 (
    echo 错误: Python无法正常运行
    echo 错误: Python无法正常运行 >> "%LOG_FILE%"
    echo 建议安装Microsoft Visual C++ Redistributable
    goto error_exit
)

echo √ Python环境正常
echo √ Python环境正常 >> "%LOG_FILE%"

:: 配置Streamlit
echo.
echo 配置Streamlit...
echo 配置Streamlit... >> "%LOG_FILE%"

set "STREAMLIT_DIR=%USERPROFILE%\.streamlit"
set "CREDENTIAL_FILE=%STREAMLIT_DIR%\credentials.toml"

if not exist "%STREAMLIT_DIR%" (
    mkdir "%STREAMLIT_DIR%" 2>nul
    if !errorlevel! equ 0 (
        echo √ 创建Streamlit配置目录
        echo √ 创建Streamlit配置目录 >> "%LOG_FILE%"
    )
)

if not exist "%CREDENTIAL_FILE%" (
    if exist "%STREAMLIT_DIR%" (
        (
            echo [general]
            echo email=""
        ) > "%CREDENTIAL_FILE%" 2>nul
        if !errorlevel! equ 0 (
            echo √ 创建Streamlit凭证文件
            echo √ 创建Streamlit凭证文件 >> "%LOG_FILE%"
        )
    )
)

:: 检查Streamlit模块
"%PYTHON_BINARY%" -c "import streamlit" >nul 2>&1
if !errorlevel! neq 0 (
    echo Streamlit模块不可用，尝试安装...
    echo Streamlit模块不可用，尝试安装... >> "%LOG_FILE%"
    "%PYTHON_BINARY%" -m pip install streamlit --quiet 2>>"%LOG_FILE%"
    if !errorlevel! neq 0 (
        echo 错误: 无法安装Streamlit
        echo 错误: 无法安装Streamlit >> "%LOG_FILE%"
        goto error_exit
    )
    echo √ Streamlit安装成功
    echo √ Streamlit安装成功 >> "%LOG_FILE%"
) else (
    echo √ Streamlit模块可用
    echo √ Streamlit模块可用 >> "%LOG_FILE%"
)

:: 启动应用
echo.
echo 准备启动应用...
echo 准备启动应用... >> "%LOG_FILE%"

cd /d "%CURRENT_DIR%NarratoAI" 2>nul
if !errorlevel! neq 0 (
    echo 错误: 无法切换到项目目录
    echo 错误: 无法切换到项目目录 >> "%LOG_FILE%"
    goto error_exit
)

echo.
echo 正在启动NarratoAI应用...
echo 启动后将自动打开浏览器窗口
echo 如果浏览器未自动打开，请手动访问: http://127.0.0.1:8501
echo.

echo 启动Streamlit应用... >> "%LOG_FILE%"
"%PYTHON_BINARY%" -m streamlit run webui.py --browser.serverAddress="127.0.0.1" --server.enableCORS=True --server.maxUploadSize=2048 --browser.gatherUsageStats=False 2>&1

set "EXIT_CODE=!errorlevel!"
echo 应用退出，退出代码: %EXIT_CODE% >> "%LOG_FILE%"

if %EXIT_CODE% neq 0 (
    echo.
    echo 应用异常退出，退出代码: %EXIT_CODE%
    echo 详细信息请查看日志: %LOG_FILE%
)

echo.
echo 按任意键退出...
pause >nul
goto :eof

:error_exit
echo.
echo 启动失败！
echo 详细信息请查看日志: %LOG_FILE%
echo.
echo 建议操作:
echo 1. 运行 check_system.bat 检查系统
echo 2. 运行 fix_issues.bat 修复问题
echo 3. 以管理员身份重新运行
echo.
echo 按任意键退出...
pause >nul
exit /b 1
