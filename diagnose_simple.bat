@echo off
setlocal enabledelayedexpansion

:: 设置编码为GBK避免乱码
chcp 936 >nul 2>&1

:: 创建日志文件
set "LOG_FILE=%~dp0diagnostic_report.txt"
set "SCRIPT_DIR=%~dp0"

echo ===============================================
echo    NarratoAI 系统诊断工具
echo ===============================================
echo.

:: 开始诊断
call :log "开始系统诊断..."
call :log "脚本目录: %SCRIPT_DIR%"
call :log "系统信息: %OS% %PROCESSOR_ARCHITECTURE%"

set ERROR_COUNT=0

:: 1. 检查关键目录
call :log ""
call :log "[1/8] 检查目录结构..."
call :check_dir "%SCRIPT_DIR%NarratoAI" "NarratoAI主目录"
call :check_dir "%SCRIPT_DIR%lib" "lib依赖目录"
call :check_dir "%SCRIPT_DIR%lib\python" "Python目录"
call :check_dir "%SCRIPT_DIR%lib\ffmpeg\ffmpeg-7.0-essentials_build" "FFmpeg目录"
call :check_dir "%SCRIPT_DIR%lib\imagemagic\ImageMagick-7.1.1-29-portable-Q16-x64" "ImageMagick目录"

:: 2. 检查关键文件
call :log ""
call :log "[2/8] 检查关键文件..."
call :check_file "%SCRIPT_DIR%NarratoAI\webui.py" "主程序文件"
call :check_file "%SCRIPT_DIR%lib\python\python.exe" "Python解释器"
call :check_file "%SCRIPT_DIR%lib\ffmpeg\ffmpeg-7.0-essentials_build\ffmpeg.exe" "FFmpeg可执行文件"
call :check_file "%SCRIPT_DIR%lib\imagemagic\ImageMagick-7.1.1-29-portable-Q16-x64\magick.exe" "ImageMagick可执行文件"

:: 3. 测试Python环境
call :log ""
call :log "[3/8] 测试Python环境..."
if exist "%SCRIPT_DIR%lib\python\python.exe" (
    "%SCRIPT_DIR%lib\python\python.exe" --version >nul 2>&1
    if !errorlevel! equ 0 (
        call :log "√ Python可以正常运行"

        :: 检查Streamlit
        "%SCRIPT_DIR%lib\python\python.exe" -c "import streamlit" >nul 2>&1
        if !errorlevel! equ 0 (
            call :log "√ Streamlit模块可用"
        ) else (
            call :log "× Streamlit模块不可用"
            set /a ERROR_COUNT+=1
        )
    ) else (
        call :log "× Python无法正常运行"
        set /a ERROR_COUNT+=1
    )
) else (
    call :log "× Python解释器不存在"
    set /a ERROR_COUNT+=1
)

:: 4. 测试FFmpeg
call :log ""
call :log "[4/8] 测试FFmpeg..."
if exist "%SCRIPT_DIR%lib\ffmpeg\ffmpeg-7.0-essentials_build\ffmpeg.exe" (
    "%SCRIPT_DIR%lib\ffmpeg\ffmpeg-7.0-essentials_build\ffmpeg.exe" -version >nul 2>&1
    if !errorlevel! equ 0 (
        call :log "✓ FFmpeg可以正常运行"
    ) else (
        call :log "✗ FFmpeg无法正常运行"
        set /a ERROR_COUNT+=1
    )
) else (
    call :log "✗ FFmpeg不存在"
    set /a ERROR_COUNT+=1
)

:: 5. 测试ImageMagick
call :log ""
call :log "[5/8] 测试ImageMagick..."
if exist "%SCRIPT_DIR%lib\imagemagic\ImageMagick-7.1.1-29-portable-Q16-x64\magick.exe" (
    "%SCRIPT_DIR%lib\imagemagic\ImageMagick-7.1.1-29-portable-Q16-x64\magick.exe" -version >nul 2>&1
    if !errorlevel! equ 0 (
        call :log "✓ ImageMagick可以正常运行"
    ) else (
        call :log "✗ ImageMagick无法正常运行"
        set /a ERROR_COUNT+=1
    )
) else (
    call :log "✗ ImageMagick不存在"
    set /a ERROR_COUNT+=1
)

:: 6. 检查权限
call :log ""
call :log "[6/8] 检查文件权限..."
echo test > "%SCRIPT_DIR%temp_test.txt" 2>nul
if exist "%SCRIPT_DIR%temp_test.txt" (
    call :log "✓ 具有写入权限"
    del "%SCRIPT_DIR%temp_test.txt" 2>nul
) else (
    call :log "✗ 缺少写入权限"
    set /a ERROR_COUNT+=1
)

:: 7. 检查Streamlit配置
call :log ""
call :log "[7/8] 检查Streamlit配置..."
set "STREAMLIT_DIR=%USERPROFILE%\.streamlit"
if not exist "%STREAMLIT_DIR%" (
    mkdir "%STREAMLIT_DIR%" 2>nul
)
if exist "%STREAMLIT_DIR%" (
    call :log "✓ Streamlit配置目录可用"
) else (
    call :log "✗ 无法创建Streamlit配置目录"
    set /a ERROR_COUNT+=1
)

:: 8. 检查端口占用
call :log ""
call :log "[8/8] 检查端口占用..."
netstat -an 2>nul | findstr ":8501" >nul
if !errorlevel! equ 0 (
    call :log "⚠ 警告: 端口8501可能被占用"
) else (
    call :log "✓ 端口8501可用"
)

:: 输出诊断结果
call :log ""
call :log "=============================================="
call :log "诊断完成！"
call :log ""

if %ERROR_COUNT% equ 0 (
    call :log "✓ 未发现严重错误，系统应该可以正常运行"
    call :log "如果仍然遇到问题，请："
    call :log "  1. 检查防病毒软件是否阻止了程序运行"
    call :log "  2. 以管理员身份运行脚本"
    call :log "  3. 检查Windows防火墙设置"
) else (
    call :log "✗ 发现 %ERROR_COUNT% 个错误，需要修复后才能正常运行"
    call :log "请查看上述错误信息并进行相应修复"
)

call :log ""
call :log "详细报告已保存到: %LOG_FILE%"
echo.
echo 按任意键退出...
pause >nul
goto :eof

:: =============================================================================
:: 辅助函数
:: =============================================================================

:log
echo %~1
echo %~1 >> "%LOG_FILE%"
goto :eof

:check_dir
if exist "%~1" (
    call :log "✓ %~2 存在"
) else (
    call :log "✗ %~2 不存在: %~1"
    set /a ERROR_COUNT+=1
)
goto :eof

:check_file
if exist "%~1" (
    call :log "✓ %~2 存在"
) else (
    call :log "✗ %~2 不存在: %~1"
    set /a ERROR_COUNT+=1
)
goto :eof
